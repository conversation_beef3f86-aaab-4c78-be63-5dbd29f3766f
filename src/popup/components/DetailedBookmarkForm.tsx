import React, { useState } from 'react'
import { useForm } from 'react-hook-form'
import { 
  ArrowLeft, 
  Save, 
  Sparkles, 
  Tag, 
  Folder, 
  FileText,
  Link,
  Loader2,
  X
} from 'lucide-react'

// shadcn组件导入
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import AITextGenerator from '../../components/AITextGenerator'
import AIRecommendations from '../../components/AIRecommendations'

interface BookmarkFormData {
  title: string
  url: string
  description: string
  tags: string[]
  category: string
  notes: string
}

interface DetailedBookmarkFormProps {
  initialData?: Partial<BookmarkFormData>
  onSave: (data: BookmarkFormData) => Promise<void>
  onCancel: () => void
  loading?: boolean
  isEditing?: boolean
}

const DetailedBookmarkForm: React.FC<DetailedBookmarkFormProps> = ({
  initialData = {},
  onSave,
  onCancel,
  loading = false,
  isEditing = false
}) => {
  // 使用react-hook-form管理表单状态
  const form = useForm<BookmarkFormData>({
    defaultValues: {
      title: initialData.title || '',
      url: initialData.url || '',
      description: initialData.description || '',
      tags: initialData.tags || [],
      category: initialData.category || '默认分类',
      notes: initialData.notes || ''
    }
  })

  const [tagInput, setTagInput] = useState('')
  const [aiLoading, setAiLoading] = useState(false)
  const [aiTagsLoading, setAiTagsLoading] = useState(false)
  const [aiTagsStatus, setAiTagsStatus] = useState<'idle' | 'loading' | 'fallback' | 'complete'>('idle')
  const [categories] = useState(['默认分类', '工作', '学习', '娱乐', '工具', '资源'])
  const [suggestedTags, setSuggestedTags] = useState<string[]>([])
  const [showAIRecommendations, setShowAIRecommendations] = useState(false)

  // 获取当前表单值
  const watchedValues = form.watch()

  // 监听AI优化完成消息
  useEffect(() => {
    const handleMessage = (message: any) => {
      if (message.type === 'AI_TAGS_OPTIMIZED') {
        console.log('收到AI优化完成消息:', message.data)

        // 检查是否是当前请求的优化结果
        const { originalRequest, optimizedResult } = message.data
        if (originalRequest.title === watchedValues.title &&
            originalRequest.url === watchedValues.url) {

          // 更新标签
          const currentTags = form.getValues('tags') || []
          const newTags = optimizedResult.tags.filter(tag => !currentTags.includes(tag))

          if (newTags.length > 0) {
            form.setValue('tags', [...currentTags, ...newTags])
            setAiTagsStatus('complete')
            console.log('AI优化完成，已更新标签:', newTags)
          } else {
            setAiTagsStatus('complete')
          }

          // 3秒后自动清除完成状态
          setTimeout(() => {
            setAiTagsStatus('idle')
          }, 3000)
        }
      }
    }

    // 添加消息监听器
    chrome.runtime.onMessage.addListener(handleMessage)

    // 清理函数
    return () => {
      chrome.runtime.onMessage.removeListener(handleMessage)
    }
  }, [watchedValues.title, watchedValues.url, form])

  // AI按钮状态辅助函数
  const getAiButtonText = () => {
    if (aiTagsLoading) {
      return 'AI生成中...'
    }
    if (aiTagsStatus === 'fallback') {
      return 'AI优化中...'
    }
    return 'AI生成'
  }

  const getAiButtonTitle = () => {
    if (aiTagsLoading) {
      return "AI正在生成标签，请稍候..."
    }
    if (aiTagsStatus === 'fallback') {
      return "已使用快速生成，AI正在后台优化标签"
    }
    return "使用AI生成相关标签"
  }

  // 添加标签
  const handleAddTag = (tag: string) => {
    const trimmedTag = tag.trim()
    const currentTags = form.getValues('tags')
    if (trimmedTag && !currentTags.includes(trimmedTag)) {
      form.setValue('tags', [...currentTags, trimmedTag])
    }
    setTagInput('')
  }

  // 删除标签
  const handleRemoveTag = (tagToRemove: string) => {
    const currentTags = form.getValues('tags')
    form.setValue('tags', currentTags.filter(tag => tag !== tagToRemove))
  }

  // 处理标签输入键盘事件
  const handleTagInputKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' || e.key === ',') {
      e.preventDefault()
      if (tagInput.trim()) {
        handleAddTag(tagInput)
      }
    } else if (e.key === 'Backspace' && !tagInput && watchedValues.tags && watchedValues.tags.length > 0) {
      handleRemoveTag(watchedValues.tags[watchedValues.tags.length - 1])
    }
  }

  // AI辅助生成标签和分类
  const handleAIAssist = async () => {
    const currentValues = form.getValues()
    if (!currentValues.title && !currentValues.description) {
      return
    }

    try {
      setAiLoading(true)
      
      // 向background script发送AI请求
      const response = await chrome.runtime.sendMessage({
        type: 'AI_GENERATE_SUGGESTIONS',
        data: {
          title: currentValues.title,
          url: currentValues.url,
          description: currentValues.description
        }
      })

      if (response?.success) {
        // 设置AI建议的标签
        if (response.data.tags) {
          setSuggestedTags(response.data.tags)
        }
        
        // 设置AI建议的分类
        if (response.data.category) {
          form.setValue('category', response.data.category)
        }

        // 设置AI生成的描述
        if (response.data.description && !currentValues.description) {
          form.setValue('description', response.data.description)
        }
      }
    } catch (error) {
      console.error('AI辅助生成失败:', error)
    } finally {
      setAiLoading(false)
    }
  }

  // 应用建议的标签
  const handleApplySuggestedTag = (tag: string) => {
    handleAddTag(tag)
    setSuggestedTags(prev => prev.filter(t => t !== tag))
  }

  // 处理AI推荐的标签选择
  const handleAITagSelect = (tag: string) => {
    handleAddTag(tag)
  }

  // 处理AI推荐的标签取消选择
  const handleAITagDeselect = (tag: string) => {
    handleRemoveTag(tag)
  }

  // 处理AI推荐的文件夹选择
  const handleAIFolderSelect = (folder: string) => {
    form.setValue('category', folder)
  }

  // 处理AI推荐的描述选择
  const handleAIDescriptionSelect = (description: string) => {
    form.setValue('description', description)
  }

  // 切换AI推荐显示
  const toggleAIRecommendations = () => {
    setShowAIRecommendations(prev => !prev)
  }

  // 提交表单
  const handleSubmit = async (data: BookmarkFormData) => {
    try {
      await onSave(data)
    } catch (error) {
      console.error('保存收藏失败:', error)
    }
  }

  return (
    <div className="w-full bg-background container-constrained">
      {/* 头部 */}
      <div className="bg-gradient-to-r from-primary to-primary/90 text-primary-foreground p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Button
              variant="ghost"
              size="icon"
              onClick={onCancel}
              className="text-primary-foreground hover:bg-background/20"
            >
              <ArrowLeft className="w-4 h-4" />
            </Button>
            <h1 className="text-lg font-semibold">{isEditing ? '编辑收藏' : '详细收藏'}</h1>
          </div>
          <div className="flex space-x-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={handleAIAssist}
              disabled={aiLoading || (!watchedValues.title && !watchedValues.description)}
              className="text-primary-foreground hover:bg-background/20"
            >
              {aiLoading ? (
                <Loader2 className="w-3 h-3 animate-spin mr-1" />
              ) : (
                <Sparkles className="w-3 h-3 mr-1" />
              )}
              AI助手
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={toggleAIRecommendations}
              disabled={!watchedValues.title && !watchedValues.description && !watchedValues.url}
              className="text-primary-foreground hover:bg-background/20"
            >
              <Sparkles className="w-3 h-3 mr-1" />
              {showAIRecommendations ? '隐藏推荐' : '智能推荐'}
            </Button>
          </div>
        </div>
      </div>

      {/* AI推荐区域 */}
      {showAIRecommendations && (
        <div className="bg-muted/30 p-4 border-b">
          <AIRecommendations
            request={{
              title: watchedValues.title,
              url: watchedValues.url,
              content: watchedValues.description,
              description: watchedValues.notes,
              maxRecommendations: 6
            }}
            selectedTags={watchedValues.tags || []}
            selectedFolder={watchedValues.category}
            currentDescription={watchedValues.description}
            onTagSelect={handleAITagSelect}
            onTagDeselect={handleAITagDeselect}
            onFolderSelect={handleAIFolderSelect}
            onDescriptionSelect={handleAIDescriptionSelect}
            disabled={loading}
            showTagRecommendations={true}
            showFolderRecommendations={true}
            showDescriptionGeneration={true}
          />
        </div>
      )}

      {/* 表单内容 */}
      <Form {...form}>
        <form onSubmit={form.handleSubmit(handleSubmit)} className="p-4 space-y-4">
          {/* 标题 */}
          <FormField
            control={form.control}
            name="title"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="flex items-center">
                  <Link className="w-4 h-4 mr-1" />
                  标题 *
                </FormLabel>
                <FormControl>
                  <Input
                    placeholder="请输入收藏标题"
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
            rules={{ required: "标题不能为空" }}
          />

          {/* URL */}
          <FormField
            control={form.control}
            name="url"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="flex items-center">
                  <Link className="w-4 h-4 mr-1" />
                  链接
                </FormLabel>
                <FormControl>
                  <Input
                    type="url"
                    placeholder="https://example.com"
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* 描述 - 集成AI生成功能 */}
          <FormField
            control={form.control}
            name="description"
            render={({ field }) => (
              <FormItem>
                <AITextGenerator
                  label={
                    <div className="flex items-center">
                      <FileText className="w-4 h-4 mr-1" />
                      描述
                    </div>
                  }
                  placeholder="简要描述这个收藏的内容..."
                  value={field.value || ''}
                  onChange={field.onChange}
                  context={{
                    title: watchedValues.title,
                    url: watchedValues.url,
                    category: watchedValues.category,
                    tags: watchedValues.tags
                  }}
                  generationType="description"
                  disabled={loading}
                  maxRows={3}
                  showSuggestions={true}
                />
                <FormMessage />
              </FormItem>
            )}
          />

          {/* 分类选择 */}
          <FormField
            control={form.control}
            name="category"
            render={({ field }) => (
              <FormItem>
                <div className="flex items-center justify-between mb-2">
                  <FormLabel className="flex items-center">
                    <Folder className="w-4 h-4 mr-1" />
                    分类
                  </FormLabel>
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={async () => {
                      try {
                        setAiLoading(true)
                        const response = await chrome.runtime.sendMessage({
                          type: 'AI_GENERATE_CATEGORY',
                          data: {
                            title: watchedValues.title,
                            url: watchedValues.url,
                            description: watchedValues.description,
                            content: watchedValues.notes
                          }
                        })
                        if (response?.success && response.data?.category) {
                          form.setValue('category', response.data.category)
                        }
                      } catch (error) {
                        console.error('AI生成分类失败:', error)
                      } finally {
                        setAiLoading(false)
                      }
                    }}
                    disabled={aiLoading || (!watchedValues.title && !watchedValues.description)}
                    className="h-7 px-2 text-xs"
                  >
                    {aiLoading ? (
                      <Loader2 className="w-3 h-3 animate-spin mr-1" />
                    ) : (
                      <Sparkles className="w-3 h-3 mr-1" />
                    )}
                    AI生成
                  </Button>
                </div>
                <Select onValueChange={field.onChange} defaultValue={field.value}>
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="选择分类" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {categories.map(category => (
                      <SelectItem key={category} value={category}>
                        {category}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* 标签输入 */}
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <Label className="flex items-center">
                <Tag className="w-4 h-4 mr-1" />
                标签
              </Label>
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={async () => {
                  try {
                    // 立即设置加载状态
                    setAiTagsLoading(true)
                    setAiTagsStatus('loading')

                    // 调用AI生成标签
                    const response = await chrome.runtime.sendMessage({
                      type: 'AI_GENERATE_TAGS',
                      data: {
                        title: watchedValues.title,
                        url: watchedValues.url,
                        description: watchedValues.description,
                        content: watchedValues.notes,
                        maxTags: 5
                      }
                    })

                    if (response?.success && response.data?.tags) {
                      // 将生成的标签添加到现有标签中（去重）
                      const currentTags = form.getValues('tags') || []
                      const newTags = response.data.tags.filter(tag => !currentTags.includes(tag))
                      form.setValue('tags', [...currentTags, ...newTags])

                      // 根据响应类型设置状态
                      if (response.data.reasoning?.includes('本地规则')) {
                        setAiTagsStatus('fallback')
                        console.log(`使用降级策略生成了 ${newTags.length} 个标签:`, newTags)
                      } else {
                        setAiTagsStatus('complete')
                        console.log(`AI生成了 ${newTags.length} 个新标签:`, newTags)
                      }
                    } else {
                      throw new Error(response?.error || 'AI生成失败')
                    }
                  } catch (error) {
                    console.error('AI生成标签失败:', error)
                    setAiTagsStatus('idle')

                    // 显示用户友好的错误提示
                    const errorMessage = error instanceof Error ? error.message : 'AI生成失败，请稍后重试'
                    console.warn('用户提示:', errorMessage)
                  } finally {
                    setAiTagsLoading(false)
                  }
                }}
                disabled={aiTagsLoading || (!watchedValues.title && !watchedValues.description)}
                className="h-7 px-2 text-xs"
                title={getAiButtonTitle()}
              >
                {aiTagsLoading ? (
                  <Loader2 className="w-3 h-3 animate-spin mr-1" />
                ) : (
                  <Sparkles className="w-3 h-3 mr-1" />
                )}
                {getAiButtonText()}
              </Button>
            </div>

            {/* AI状态提示 */}
            {aiTagsStatus === 'fallback' && (
              <div className="text-xs text-muted-foreground mb-2 flex items-center">
                <Loader2 className="w-3 h-3 animate-spin mr-1" />
                已使用快速生成，AI正在后台优化标签...
              </div>
            )}

            {aiTagsStatus === 'complete' && (
              <div className="text-xs text-green-600 mb-2 flex items-center">
                <svg className="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                </svg>
                AI标签生成完成
              </div>
            )}

            {/* 已添加的标签 */}
            {watchedValues.tags && watchedValues.tags.length > 0 && (
              <div className="flex flex-wrap gap-1 mb-2">
                {watchedValues.tags.map((tag, index) => (
                  <Badge
                    key={index}
                    variant="secondary"
                    className="text-xs"
                  >
                    {tag}
                    <Button
                      type="button"
                      variant="ghost"
                      size="icon"
                      onClick={() => handleRemoveTag(tag)}
                      className="ml-1 h-3 w-3 p-0 hover:bg-transparent"
                    >
                      <X className="h-2 w-2" />
                    </Button>
                  </Badge>
                ))}
              </div>
            )}

            {/* 标签输入框 */}
            <Input
              type="text"
              value={tagInput}
              onChange={(e) => setTagInput(e.target.value)}
              onKeyDown={handleTagInputKeyDown}
              placeholder="输入标签后按回车或逗号添加"
            />

            {/* AI建议的标签 */}
            {suggestedTags.length > 0 && (
              <div className="space-y-1">
                <Label className="text-xs text-muted-foreground">AI建议的标签：</Label>
                <div className="flex flex-wrap gap-1">
                  {suggestedTags.map((tag, index) => (
                    <Badge
                      key={index}
                      variant="outline"
                      className="text-xs cursor-pointer hover:bg-accent"
                      onClick={() => handleApplySuggestedTag(tag)}
                    >
                      + {tag}
                    </Badge>
                  ))}
                </div>
              </div>
            )}
          </div>

          {/* 笔记 - 集成AI生成功能 */}
          <FormField
            control={form.control}
            name="notes"
            render={({ field }) => (
              <FormItem>
                <AITextGenerator
                  label={
                    <div className="flex items-center">
                      <FileText className="w-4 h-4 mr-1" />
                      笔记
                    </div>
                  }
                  placeholder="添加个人笔记或想法..."
                  value={field.value || ''}
                  onChange={field.onChange}
                  context={{
                    title: watchedValues.title,
                    url: watchedValues.url,
                    description: watchedValues.description,
                    category: watchedValues.category,
                    tags: watchedValues.tags
                  }}
                  generationType="notes"
                  disabled={loading}
                  maxRows={3}
                  showSuggestions={true}
                />
                <FormMessage />
              </FormItem>
            )}
          />

          {/* 提交按钮 */}
          <div className="flex space-x-2 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={onCancel}
              className="flex-1"
            >
              取消
            </Button>
            <Button
              type="submit"
              disabled={loading || !watchedValues.title?.trim()}
              className="flex-1"
            >
              {loading ? (
                <>
                  <Loader2 className="w-4 h-4 animate-spin mr-2" />
                  保存中...
                </>
              ) : (
                <>
                  <Save className="w-4 h-4 mr-2" />
                  {isEditing ? '保存修改' : '保存收藏'}
                </>
              )}
            </Button>
          </div>
        </form>
      </Form>
    </div>
  )
}

export default DetailedBookmarkForm